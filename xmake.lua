-- xmake.lua (GUI対応版)
add_rules("mode.debug", "mode.release")
set_project("HeartRateViewer")
set_version("1.0.0")

set_languages("cxx17")

-- NixOSではシステムパッケージを使用するため、add_requiresをコメントアウト
-- add_requires("n<PERSON><PERSON>_json", { optional = true })
-- add_requires("cpr", { optional = true })
-- add_requires("imgui", {configs = {glfw = true, opengl3 = true}})
-- add_requires("websocketpp")
-- add_requires("openssl")
-- add_requires("boost", {configs = {all = true}})

target("heart-rate-viewer") do
    set_kind("binary")
    add_files("src/*.cpp")
    add_includedirs("src")

    -- NixOSでシステムライブラリを直接リンク
    if is_plat("linux") then
        -- OpenGL関連
        add_syslinks("GL", "GLEW", "glfw")

        -- X11関連
        add_syslinks("X11", "Xcursor", "Xrandr", "Xi")

        -- SSL/TLS
        add_syslinks("ssl", "crypto")

        -- システムライブラリ
        add_syslinks("dl", "pthread")

        -- Boost (必要な部分のみ)
        add_syslinks("boost_system", "boost_thread")
    end
end