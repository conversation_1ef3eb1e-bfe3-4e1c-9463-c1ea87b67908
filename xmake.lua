-- xmake.lua (NixOS対応版)
add_rules("mode.debug", "mode.release")
set_project("HeartRateViewer")
set_version("1.0.0")

set_languages("cxx17")

-- NixOSではシステムパッケージを使用
add_requires("glfw", "opengl", "imgui[glfw_opengl3]", "websocketpp")

-- GUI版（後で実装）
target("heart-rate-viewer") do
    set_kind("binary")
    add_files("src/main.cpp", "src/HeartRateMonitor.cpp")
    add_includedirs("src")

    if is_plat("linux") then
        add_packages("openssl", "boost", "glfw", "opengl", "imgui", "websocketpp")
        add_syslinks("GL", "GLEW", "glfw", "ssl", "crypto", "X11", "Xrandr", "Xinerama", "Xcursor", "pthread", "dl")
        add_includedirs("/nix/store/*/include")
    end
end
