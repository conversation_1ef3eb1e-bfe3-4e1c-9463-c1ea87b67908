#pragma once

#include <string>
#include <thread>
#include <atomic>
#include <memory>
#include <chrono>

// Forward declarations
extern std::atomic<int> g_currentHeartRate;
extern std::atomic<bool> g_isConnected;
extern std::string g_connectionStatus;

class HeartRateMonitorHTTP
{
public:
    explicit HeartRateMonitorHTTP(const std::string& accessToken);
    ~HeartRateMonitorHTTP();

    void start();
    void stop();
    bool isConnected() const;

private:
    void run();
    void fetchHeartRate();

    std::string m_accessToken;
    std::string m_apiUrl;
    std::thread m_thread;
    std::atomic<bool> m_running;
    std::atomic<bool> m_connected;
    std::chrono::milliseconds m_pollInterval;
};
