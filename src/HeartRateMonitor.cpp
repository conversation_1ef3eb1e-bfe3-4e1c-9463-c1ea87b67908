#include "HeartRateMonitor.h"
#include <iostream>
#include <sstream>

HeartRateMonitor::HeartRateMonitor(const std::string& accessToken)
    : m_accessToken(accessToken)
    , m_running(false)
    , m_connected(false)
{
    // Construct the WebSocket URI
    m_uri = "wss://dev.pulsoid.net/api/v1/data/real_time?access_token=" + 
            m_accessToken + "&response_mode=text_plain_only_heart_rate";

    // Set logging settings
    m_client.set_access_channels(websocketpp::log::alevel::all);
    m_client.clear_access_channels(websocketpp::log::alevel::frame_payload);

    // Initialize ASIO
    m_client.init_asio();

    // Set TLS init handler
    m_client.set_tls_init_handler(
        std::bind(&HeartRateMonitor::onTlsInit, this, std::placeholders::_1)
    );

    // Set handlers
    m_client.set_open_handler(
        std::bind(&HeartRateMonitor::onOpen, this, std::placeholders::_1)
    );
    
    m_client.set_close_handler(
        std::bind(&HeartRateMonitor::onClose, this, std::placeholders::_1)
    );
    
    m_client.set_message_handler(
        std::bind(&HeartRateMonitor::onMessage, this, std::placeholders::_1, std::placeholders::_2)
    );
    
    m_client.set_fail_handler(
        std::bind(&HeartRateMonitor::onFail, this, std::placeholders::_1)
    );
}

HeartRateMonitor::~HeartRateMonitor()
{
    stop();
}

void HeartRateMonitor::start()
{
    if (m_running)
        return;

    m_running = true;
    m_thread = std::thread(&HeartRateMonitor::run, this);
    
    g_connectionStatus = "Connecting...";
}

void HeartRateMonitor::stop()
{
    if (!m_running)
        return;

    m_running = false;
    
    if (m_connected)
    {
        websocketpp::lib::error_code ec;
        m_client.close(m_hdl, websocketpp::close::status::going_away, "", ec);
        if (ec)
        {
            std::cout << "Error closing connection: " << ec.message() << std::endl;
        }
    }

    m_client.stop();

    if (m_thread.joinable())
    {
        m_thread.join();
    }

    m_connected = false;
    g_isConnected = false;
    g_connectionStatus = "Disconnected";
}

bool HeartRateMonitor::isConnected() const
{
    return m_connected;
}

void HeartRateMonitor::run()
{
    try
    {
        websocketpp::lib::error_code ec;
        client::connection_ptr con = m_client.get_connection(m_uri, ec);
        
        if (ec)
        {
            std::cout << "Could not create connection: " << ec.message() << std::endl;
            g_connectionStatus = "Connection failed: " + ec.message();
            return;
        }

        m_hdl = con->get_handle();
        m_client.connect(con);

        // Start the ASIO io_service run loop
        m_client.run();
    }
    catch (websocketpp::exception const& e)
    {
        std::cout << "WebSocket exception: " << e.what() << std::endl;
        g_connectionStatus = "Exception: " + std::string(e.what());
    }
    catch (std::exception const& e)
    {
        std::cout << "Standard exception: " << e.what() << std::endl;
        g_connectionStatus = "Exception: " + std::string(e.what());
    }
}

void HeartRateMonitor::onOpen(websocketpp::connection_hdl hdl)
{
    std::cout << "Connection opened" << std::endl;
    m_connected = true;
    g_isConnected = true;
    g_connectionStatus = "Connected";
}

void HeartRateMonitor::onClose(websocketpp::connection_hdl hdl)
{
    std::cout << "Connection closed" << std::endl;
    m_connected = false;
    g_isConnected = false;
    g_connectionStatus = "Disconnected";
}

void HeartRateMonitor::onMessage(websocketpp::connection_hdl hdl, message_ptr msg)
{
    try
    {
        std::string payload = msg->get_payload();
        std::cout << "Received: " << payload << std::endl;

        // Parse the heart rate value
        // The API returns plain text with just the heart rate number
        int heartRate = std::stoi(payload);
        
        if (heartRate > 0 && heartRate < 300) // Sanity check
        {
            g_currentHeartRate = heartRate;
            std::cout << "Heart rate updated: " << heartRate << " BPM" << std::endl;
        }
    }
    catch (const std::exception& e)
    {
        std::cout << "Error parsing heart rate: " << e.what() << std::endl;
    }
}

void HeartRateMonitor::onFail(websocketpp::connection_hdl hdl)
{
    std::cout << "Connection failed" << std::endl;
    m_connected = false;
    g_isConnected = false;
    g_connectionStatus = "Connection failed";
}

HeartRateMonitor::context_ptr HeartRateMonitor::onTlsInit(websocketpp::connection_hdl hdl)
{
    context_ptr ctx = websocketpp::lib::make_shared<websocketpp::lib::asio::ssl::context>(
        websocketpp::lib::asio::ssl::context::sslv23
    );

    try
    {
        ctx->set_options(websocketpp::lib::asio::ssl::context::default_workarounds |
                        websocketpp::lib::asio::ssl::context::no_sslv2 |
                        websocketpp::lib::asio::ssl::context::no_sslv3 |
                        websocketpp::lib::asio::ssl::context::single_dh_use);
    }
    catch (std::exception& e)
    {
        std::cout << "TLS init error: " << e.what() << std::endl;
    }

    return ctx;
}
