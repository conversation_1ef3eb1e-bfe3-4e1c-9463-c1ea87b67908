#include "HeartRateMonitorHTTP.h"
#include <iostream>
#include <cpr/cpr.h>
#include <nlohmann/json.hpp>

HeartRateMonitorHTTP::HeartRateMonitorHTTP(const std::string& accessToken)
    : m_accessToken(accessToken)
    , m_running(false)
    , m_connected(false)
    , m_pollInterval(std::chrono::milliseconds(1000)) // Poll every 1 second
{
    // Pulsoid HTTP API endpoint
    m_apiUrl = "https://dev.pulsoid.net/api/v1/data/heart_rate/latest?access_token=" + m_accessToken;
}

HeartRateMonitorHTTP::~HeartRateMonitorHTTP()
{
    stop();
}

void HeartRateMonitorHTTP::start()
{
    if (m_running)
        return;

    m_running = true;
    m_thread = std::thread(&HeartRateMonitorHTTP::run, this);
    
    g_connectionStatus = "Connecting...";
}

void HeartRateMonitorHTTP::stop()
{
    if (!m_running)
        return;

    m_running = false;

    if (m_thread.joinable())
    {
        m_thread.join();
    }

    m_connected = false;
    g_isConnected = false;
    g_connectionStatus = "Disconnected";
}

bool HeartRateMonitorHTTP::isConnected() const
{
    return m_connected;
}

void HeartRateMonitorHTTP::run()
{
    std::cout << "Starting HTTP polling for heart rate data..." << std::endl;
    
    while (m_running)
    {
        try
        {
            fetchHeartRate();
        }
        catch (const std::exception& e)
        {
            std::cout << "Error fetching heart rate: " << e.what() << std::endl;
            g_connectionStatus = "Error: " + std::string(e.what());
            m_connected = false;
            g_isConnected = false;
        }

        // Sleep for the poll interval
        std::this_thread::sleep_for(m_pollInterval);
    }
}

void HeartRateMonitorHTTP::fetchHeartRate()
{
    // Make HTTP GET request
    auto response = cpr::Get(cpr::Url{m_apiUrl});

    if (response.status_code == 200)
    {
        try
        {
            // Parse JSON response
            auto json = nlohmann::json::parse(response.text);
            
            if (json.contains("data") && json["data"].contains("heart_rate"))
            {
                int heartRate = json["data"]["heart_rate"];
                
                if (heartRate > 0 && heartRate < 300) // Sanity check
                {
                    g_currentHeartRate = heartRate;
                    m_connected = true;
                    g_isConnected = true;
                    g_connectionStatus = "Connected (HTTP)";
                    
                    std::cout << "Heart rate updated: " << heartRate << " BPM" << std::endl;
                }
                else
                {
                    std::cout << "Invalid heart rate value: " << heartRate << std::endl;
                }
            }
            else
            {
                std::cout << "No heart rate data in response" << std::endl;
                std::cout << "Response: " << response.text << std::endl;
            }
        }
        catch (const nlohmann::json::exception& e)
        {
            std::cout << "JSON parsing error: " << e.what() << std::endl;
            std::cout << "Response: " << response.text << std::endl;
        }
    }
    else if (response.status_code == 401)
    {
        std::cout << "Authentication failed. Please check your access token." << std::endl;
        g_connectionStatus = "Authentication failed";
        m_connected = false;
        g_isConnected = false;
    }
    else
    {
        std::cout << "HTTP request failed with status: " << response.status_code << std::endl;
        std::cout << "Response: " << response.text << std::endl;
        g_connectionStatus = "HTTP Error: " + std::to_string(response.status_code);
        m_connected = false;
        g_isConnected = false;
    }
}
