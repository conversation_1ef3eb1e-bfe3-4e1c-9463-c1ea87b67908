{
    swift_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    },
    yasm_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    },
    cuda_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    },
    gcc_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = {
            name = "gcc",
            program = "/run/current-system/sw/bin/gcc"
        }
    },
    fasm_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    },
    fpc_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    },
    go_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    },
    cross_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = false
    },
    rust_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    },
    nim_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = false
    },
    gfortran_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    },
    envs_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    },
    nasm_arch_x86_64_plat_linux = {
        plat = "linux",
        __global = true,
        arch = "x86_64",
        __checked = true
    }
}