#pragma once

#include <string>
#include <thread>
#include <atomic>
#include <memory>
#include <functional>

// WebSocket includes
#include <websocketpp/config/asio_client.hpp>
#include <websocketpp/client.hpp>

// JSON includes
#include <nlohmann/json.hpp>

// Forward declarations
extern std::atomic<int> g_currentHeartRate;
extern std::atomic<bool> g_isConnected;
extern std::string g_connectionStatus;

class HeartRateMonitor
{
public:
    using client = websocketpp::client<websocketpp::config::asio_tls_client>;
    using message_ptr = websocketpp::config::asio_tls_client::message_type::ptr;
    using context_ptr = websocketpp::lib::shared_ptr<websocketpp::lib::asio::ssl::context>;

    explicit HeartRateMonitor(const std::string& accessToken);
    ~HeartRateMonitor();

    void start();
    void stop();
    bool isConnected() const;

private:
    void run();
    void onOpen(websocketpp::connection_hdl hdl);
    void onClose(websocketpp::connection_hdl hdl);
    void onMessage(websocketpp::connection_hdl hdl, message_ptr msg);
    void onFail(websocketpp::connection_hdl hdl);
    context_ptr onTlsInit(websocketpp::connection_hdl hdl);

    std::string m_accessToken;
    std::string m_uri;
    client m_client;
    websocketpp::connection_hdl m_hdl;
    std::thread m_thread;
    std::atomic<bool> m_running;
    std::atomic<bool> m_connected;
};
