set(CMAKE_C_COMPILER "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/gcc")
set(CMAKE_C_COMPILER_ARG1 "")
set(CMAKE_C_COMPILER_ID "GNU")
set(CMAKE_C_COMPILER_VERSION "14.3.0")
set(CMAKE_C_COMPILER_VERSION_INTERNAL "")
set(CMAKE_C_COMPILER_WRAPPER "")
set(CMAKE_C_STANDARD_COMPUTED_DEFAULT "17")
set(CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT "ON")
set(CMAKE_C_STANDARD_LATEST "23")
set(CMAKE_C_COMPILE_FEATURES "c_std_90;c_function_prototypes;c_std_99;c_restrict;c_variadic_macros;c_std_11;c_static_assert;c_std_17;c_std_23")
set(CMAKE_C90_COMPILE_FEATURES "c_std_90;c_function_prototypes")
set(CMAKE_C99_COMPILE_FEATURES "c_std_99;c_restrict;c_variadic_macros")
set(CMAKE_C11_COMPILE_FEATURES "c_std_11;c_static_assert")
set(CMAKE_C17_COMPILE_FEATURES "c_std_17")
set(CMAKE_C23_COMPILE_FEATURES "c_std_23")

set(CMAKE_C_PLATFORM_ID "Linux")
set(CMAKE_C_SIMULATE_ID "")
set(CMAKE_C_COMPILER_FRONTEND_VARIANT "GNU")
set(CMAKE_C_SIMULATE_VERSION "")




set(CMAKE_AR "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/ar")
set(CMAKE_C_COMPILER_AR "/nix/store/xvqbvva4djgyascjsnki6354422n4msk-gcc-14.3.0/bin/gcc-ar")
set(CMAKE_RANLIB "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/ranlib")
set(CMAKE_C_COMPILER_RANLIB "/nix/store/xvqbvva4djgyascjsnki6354422n4msk-gcc-14.3.0/bin/gcc-ranlib")
set(CMAKE_LINKER "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/ld")
set(CMAKE_LINKER_LINK "")
set(CMAKE_LINKER_LLD "")
set(CMAKE_C_COMPILER_LINKER "/nix/store/svk6gmy4wbf0y2v8j3g8fw0jx6zhixqf-binutils-wrapper-2.44/bin/ld")
set(CMAKE_C_COMPILER_LINKER_ID "GNU")
set(CMAKE_C_COMPILER_LINKER_VERSION 2.44)
set(CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT GNU)
set(CMAKE_MT "")
set(CMAKE_TAPI "CMAKE_TAPI-NOTFOUND")
set(CMAKE_COMPILER_IS_GNUCC 1)
set(CMAKE_C_COMPILER_LOADED 1)
set(CMAKE_C_COMPILER_WORKS TRUE)
set(CMAKE_C_ABI_COMPILED TRUE)

set(CMAKE_C_COMPILER_ENV_VAR "CC")

set(CMAKE_C_COMPILER_ID_RUN 1)
set(CMAKE_C_SOURCE_FILE_EXTENSIONS c;m)
set(CMAKE_C_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_C_LINKER_PREFERENCE 10)
set(CMAKE_C_LINKER_DEPFILE_SUPPORTED TRUE)
set(CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED TRUE)
set(CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED TRUE)

# Save compiler ABI information.
set(CMAKE_C_SIZEOF_DATA_PTR "8")
set(CMAKE_C_COMPILER_ABI "ELF")
set(CMAKE_C_BYTE_ORDER "LITTLE_ENDIAN")
set(CMAKE_C_LIBRARY_ARCHITECTURE "")

if(CMAKE_C_SIZEOF_DATA_PTR)
  set(CMAKE_SIZEOF_VOID_P "${CMAKE_C_SIZEOF_DATA_PTR}")
endif()

if(CMAKE_C_COMPILER_ABI)
  set(CMAKE_INTERNAL_PLATFORM_ABI "${CMAKE_C_COMPILER_ABI}")
endif()

if(CMAKE_C_LIBRARY_ARCHITECTURE)
  set(CMAKE_LIBRARY_ARCHITECTURE "")
endif()

set(CMAKE_C_CL_SHOWINCLUDES_PREFIX "")
if(CMAKE_C_CL_SHOWINCLUDES_PREFIX)
  set(CMAKE_CL_SHOWINCLUDES_PREFIX "${CMAKE_C_CL_SHOWINCLUDES_PREFIX}")
endif()





set(CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES "/nix/store/0xbikmlg7655qqjgavghrqzmc42s96q8-curl-8.14.1-dev/include;/nix/store/bibgd7mmkv4lf8fkmiwiaa1rysk9p9gs-brotli-1.1.0-dev/include;/nix/store/12lkraggc4wzbhlgxqmbkaliysqnv93z-krb5-1.21.3-dev/include;/nix/store/hc7xc9ypljl5yf3yfbz3pfk1zqdka65b-nghttp2-1.65.0-dev/include;/nix/store/1hgypax26basvx8qmn6vyz5zk96jbq4y-libidn2-2.3.8-dev/include;/nix/store/jyi9ix8mpg6zb22d14ibsxblyz7qm9lw-openssl-3.5.1-dev/include;/nix/store/10g5365j47gx8nl7sisylvlx558l4s13-libpsl-0.21.5-dev/include;/nix/store/w1mqwd7lf7dc9126rqf927m9l41r3r64-libssh2-1.11.1-dev/include;/nix/store/1hwa9m4q8nawlgmrj4mnjk355jjr9m12-zlib-1.3.1-dev/include;/nix/store/a4xw7mf70g626s4cba0kz3wpllw4bk1z-zstd-1.5.7-dev/include;/nix/store/d92pdqjvpzz6nmxnaaazjxwdrg1mivjx-glfw-3.4/include;/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include;/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include;/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include;/nix/store/pdj7bagpfr9spy6c32kskm3pwdzc32cz-mesa-25.1.5/include;/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include;/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include;/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include;/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include;/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include;/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include;/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include;/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include;/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include;/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include;/nix/store/0dkdzn9yj8rp2a1qah8s8ymp457cg8vq-nlohmann_json-3.11.3/include;/nix/store/0dlf3y2sz823mmbm8wvyg45a04n62zcz-cpr-1.12.0/include;/nix/store/ir0758kmac2mk5852s7cp65ss8ins92p-boost-1.87.0-dev/include;/nix/store/7kym3r8g0s6p7s23b58h4xbd8cnxw1iy-websocket++-0.8.2/include;/nix/store/j5z5d0f3fyxksvppgfd6l5ch9pby6ycb-compiler-rt-libc-16.0.6-dev/include;/nix/store/xvqbvva4djgyascjsnki6354422n4msk-gcc-14.3.0/lib/gcc/x86_64-unknown-linux-gnu/14.3.0/include;/nix/store/xvqbvva4djgyascjsnki6354422n4msk-gcc-14.3.0/include;/nix/store/xvqbvva4djgyascjsnki6354422n4msk-gcc-14.3.0/lib/gcc/x86_64-unknown-linux-gnu/14.3.0/include-fixed;/nix/store/vm10zh43xgfxvgrqs8brz6v6xyrq9qin-glibc-2.40-66-dev/include")
set(CMAKE_C_IMPLICIT_LINK_LIBRARIES "gcc;gcc_s;c;gcc;gcc_s")
set(CMAKE_C_IMPLICIT_LINK_DIRECTORIES "/nix/store/pazhpyf2g67czh2g7za2vbx20jg6fgpd-brotli-1.1.0-lib/lib;/nix/store/7razjlx084wqwcaa359mvrcjd4lx1kn2-krb5-1.21.3-lib/lib;/nix/store/fkb98s4dbrj01rx02rapzmh53pmpynmk-nghttp2-1.65.0-lib/lib;/nix/store/6d9jl70zcjdg4vp9g12mm7z5fpag6419-libidn2-2.3.8/lib;/nix/store/wadq4gzybbvg5243d90l9x4mw3459r3a-openssl-3.5.1/lib;/nix/store/165yqakqic8jjbyhsxnklgjccqnpgn6b-libpsl-0.21.5/lib;/nix/store/72j7pba9jw6b6fl6pwn38l39sv3va8h9-libssh2-1.11.1/lib;/nix/store/abch1r0gnbpikbp9n4x6mm8dwqwfrib6-zlib-1.3.1/lib;/nix/store/9kd5dyrajlqa97ifwyi38c17lrvabbvw-zstd-1.5.7/lib;/nix/store/qra9dfwfkpz7h6ng3489lsww2sr3gjyd-curl-8.14.1/lib;/nix/store/d92pdqjvpzz6nmxnaaazjxwdrg1mivjx-glfw-3.4/lib;/nix/store/iyy1g70fhkz3hsrckbmbqgxik1j9779c-libglvnd-1.7.0/lib;/nix/store/gplm5qr5p5yj38hmy77xd340wyiiah7j-glu-9.0.3/lib;/nix/store/p21apy1m82s9llfqvxq42nzqnzgcjwhi-glew-2.2.0/lib;/nix/store/pdj7bagpfr9spy6c32kskm3pwdzc32cz-mesa-25.1.5/lib;/nix/store/rcm8ry0f10l80h5l26s74p4ik5wsbzas-libxcb-1.17.0/lib;/nix/store/pahwl2rq51dmwrn8czks27yy3sa3byg9-libX11-1.8.12/lib;/nix/store/zgpm3jjfsfs1ljdzm1xjq502mkvxck3m-libXcursor-1.2.3/lib;/nix/store/sczkw2z74zm3niylbfgxh211b3cs9k9f-libXrender-0.9.12/lib;/nix/store/yp7hylmnidn1mr91xsdn2dj5glhqmk7a-libXrandr-1.5.4/lib;/nix/store/ksi1cmprh96psxs72y786w3m30l6x7wm-libXfixes-6.0.1/lib;/nix/store/zv8s9b9vq8b2shavwq7s8j5inw34d9sy-libXau-1.0.12/lib;/nix/store/844cgxkyzi1nrilvamxr08gs9l278gx9-libXext-1.3.6/lib;/nix/store/474ia8w6cpw6vmhbsvbf5zx7bx5md4bv-libXi-1.8.2/lib;/nix/store/0dlf3y2sz823mmbm8wvyg45a04n62zcz-cpr-1.12.0/lib;/nix/store/h9w24yjqbj1045j5sml80dq0q9653wpl-boost-1.87.0/lib;/nix/store/g2jzxk3s7cnkhh8yq55l4fbvf639zy37-glibc-2.40-66/lib;/nix/store/xvqbvva4djgyascjsnki6354422n4msk-gcc-14.3.0/lib/gcc/x86_64-unknown-linux-gnu/14.3.0;/nix/store/6vzcxjxa2wlh3p9f5nhbk62bl3q313ri-gcc-14.3.0-lib/lib;/nix/store/xvqbvva4djgyascjsnki6354422n4msk-gcc-14.3.0/lib")
set(CMAKE_C_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES "")
