cmake_minimum_required(VERSION 3.16)
project(HeartRateViewer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(Boost REQUIRED COMPONENTS system thread)

# Find nlohmann_json
find_package(nlohmann_json REQUIRED)

# WebSocket++ is header-only, so we just need to find it
find_path(WEBSOCKETPP_INCLUDE_DIR websocketpp/config/asio_client.hpp)

# Create simple console executable first
add_executable(heart-rate-viewer-simple
    src/simple_main.cpp
    src/HeartRateMonitor.cpp
)

# Include directories
target_include_directories(heart-rate-viewer-simple PRIVATE
    src
    ${WEBSOCKETPP_INCLUDE_DIR}
)

# Link libraries
target_link_libraries(heart-rate-viewer-simple
    OpenSSL::SSL
    OpenSSL::Crypto
    Boost::system
    Boost::thread
    nlohmann_json::n<PERSON>hmann_json
    pthread
    dl
)
