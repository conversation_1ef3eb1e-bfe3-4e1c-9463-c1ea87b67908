#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <string>
#include <atomic>

#include "HeartRateMonitor.h"

// Global variables
std::atomic<int> g_currentHeartRate{0};
std::atomic<bool> g_isConnected{false};
std::string g_connectionStatus = "Disconnected";

int main()
{
    std::cout << "Heart Rate Monitor - Simple Console Version" << std::endl;
    std::cout << "===========================================" << std::endl;
    
    std::string accessToken;
    std::cout << "Enter your Pulsoid access token: ";
    std::getline(std::cin, accessToken);
    
    if (accessToken.empty())
    {
        std::cout << "No access token provided. Exiting." << std::endl;
        return 1;
    }
    
    // Create and start heart rate monitor
    auto monitor = std::make_unique<HeartRateMonitor>(accessToken);
    monitor->start();
    
    std::cout << "Connecting to Pulsoid..." << std::endl;
    std::cout << "Press Ctrl+C to exit" << std::endl;
    std::cout << std::endl;
    
    // Main loop
    int lastHeartRate = 0;
    while (true)
    {
        int currentHR = g_currentHeartRate.load();
        bool connected = g_isConnected.load();
        
        // Clear screen and show current status
        system("clear");
        std::cout << "Heart Rate Monitor - Simple Console Version" << std::endl;
        std::cout << "===========================================" << std::endl;
        std::cout << "Status: " << g_connectionStatus << std::endl;
        std::cout << std::endl;
        
        if (connected && currentHR > 0)
        {
            std::cout << "Current Heart Rate: " << currentHR << " BPM" << std::endl;
            
            // Simple heart rate zone indication
            if (currentHR < 60)
                std::cout << "Zone: Resting" << std::endl;
            else if (currentHR < 100)
                std::cout << "Zone: Light Activity" << std::endl;
            else if (currentHR < 150)
                std::cout << "Zone: Moderate Activity" << std::endl;
            else
                std::cout << "Zone: High Intensity" << std::endl;
        }
        else
        {
            std::cout << "Current Heart Rate: -- BPM" << std::endl;
            std::cout << "Zone: Unknown" << std::endl;
        }
        
        std::cout << std::endl;
        std::cout << "Press Ctrl+C to exit" << std::endl;
        
        // Sleep for a short time
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    return 0;
}
